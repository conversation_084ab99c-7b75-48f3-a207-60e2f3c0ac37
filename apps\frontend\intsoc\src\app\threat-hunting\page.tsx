'use client';

import { <PERSON>, CardHeader, CardContent } from '@telesoft/ui';
import { ThreatFlowDiagram } from '@telesoft/d3';
import { useState, useMemo, useEffect } from 'react';
import { classNames, STYLE_PRESETS } from '@telesoft/utils';

export default function ThreatHunting() {
  const [sourceAActive, setSourceAActive] = useState(false);
  const [sourceBActive, setSourceBActive] = useState(false);
  const [processedActive, setProcessedActive] = useState(false);

  // Additional dynamic state for status cards
  const [activeHunts, setActiveHunts] = useState({
    running: 7,
    queued: 3,
    completed: 142,
  });
  const [detectionRate, setDetectionRate] = useState(94.7);
  const [systemHealth, setSystemHealth] = useState({
    processingLoad: 67,
    memoryUsage: 42,
  });

  // Continuous animation cycle - keep everything active with minor metric updates
  useEffect(() => {
    // Set everything to active immediately for constant "analyzing" state
    setSourceAActive(true);
    setSourceBActive(true);
    setProcessedActive(true);

    // Update status card metrics with minor variations
    const updateMetrics = () => {
      setActiveHunts((prev) => ({
        running: Math.max(
          5,
          Math.min(
            10,
            prev.running +
            (Math.random() > 0.7 ? (Math.random() > 0.5 ? 1 : -1) : 0),
          ),
        ),
        queued: Math.max(
          1,
          Math.min(
            8,
            prev.queued +
            (Math.random() > 0.8 ? (Math.random() > 0.5 ? 1 : -1) : 0),
          ),
        ),
        completed: prev.completed + (Math.random() > 0.9 ? 1 : 0),
      }));

      setDetectionRate((prev) =>
        Math.max(92, Math.min(98, prev + (Math.random() - 0.5) * 0.2)),
      );

      setSystemHealth((prev) => ({
        processingLoad: Math.max(
          45,
          Math.min(85, prev.processingLoad + (Math.random() - 0.5) * 3),
        ),
        memoryUsage: Math.max(
          35,
          Math.min(70, prev.memoryUsage + (Math.random() - 0.5) * 2),
        ),
      }));
    };

    // Initial update
    updateMetrics();

    // Set up continuous metric updates
    const metricsInterval = setInterval(
      () => {
        updateMetrics();
      },
      2000 + Math.random() * 2000,
    ); // Update every 2-4 seconds

    return () => {
      clearInterval(metricsInterval);
    };
  }, []);

  // D3 Flow Diagram Data - simplified to 3 source boxes
  const flowNodes = useMemo(
    () => [
      // Simplified Source Groups
      {
        id: 'threat-intelligence',
        label: 'Threat Intelligence',
        description: 'CTX, MISP & IOC Feeds',
        type: 'source' as const,
        active: sourceAActive,
      },
      {
        id: 'news',
        label: 'News',
        description: 'Threat Summaries & Intelligence',
        type: 'source' as const,
        active: sourceBActive,
      },
      {
        id: 'playbook',
        label: 'Playbook',
        description: 'Security Policies & Rules',
        type: 'source' as const,
        active: sourceBActive,
      },
      // Central Processor
      {
        id: 'processor',
        label: 'Correlation Engine',
        description: 'Advanced Analytics',
        type: 'processor' as const,
        metrics: [
          {
            label: 'Correlation Rate',
            value: processedActive
              ? `${(97.5 + Math.random() * 2).toFixed(1)}%`
              : '0%',
            status: 'good' as const,
          },
          {
            label: 'Active Rules',
            value: processedActive
              ? Math.floor(150 + Math.random() * 15).toString()
              : '0',
            status: 'good' as const,
          },
        ],
        active: processedActive,
      },
      // Outputs
      {
        id: 'output-conclusive',
        label: 'Conclusive',
        description: processedActive
          ? Math.floor(8 + Math.random() * 8).toString()
          : '0',
        type: 'output' as const,
        metrics: [],
        active: processedActive,
      },
      {
        id: 'output-inconclusive',
        label: 'Inconclusive',
        description: processedActive
          ? Math.floor(520 + Math.random() * 80).toString()
          : '0',
        type: 'output' as const,
        metrics: [],
        active: processedActive,
      },
    ],
    [sourceAActive, sourceBActive, processedActive],
  );

  const flowLinks = useMemo(
    () => [
      // Simplified links from 3 sources to processor
      {
        source: 'threat-intelligence',
        target: 'processor',
        active: sourceAActive,
      },
      { source: 'news', target: 'processor', active: sourceBActive },
      { source: 'playbook', target: 'processor', active: sourceBActive },
      // Processor to outputs
      {
        source: 'processor',
        target: 'output-conclusive',
        active: processedActive,
      },
      {
        source: 'processor',
        target: 'output-inconclusive',
        active: processedActive,
      },
    ],
    [sourceAActive, sourceBActive, processedActive],
  );

  return (
    <div className={classNames(STYLE_PRESETS.pageContainer)}>
      <div className={classNames(STYLE_PRESETS.contentContainer)}>


        {/* D3 Interactive Flow Diagram */}
        <div className="mt-0">
          <CardContent>
            <div className="bg-background-primary rounded-lg p-6 border border-border-primary flex items-center justify-center min-h-[450px]">
              <ThreatFlowDiagram
                nodes={flowNodes}
                links={flowLinks}
                width={800}
                height={400}
                onNodeClick={(node) => {
                  console.log('Node clicked:', node);
                  // Handle node interactions
                }}
                className="mx-auto"
              />
            </div>
          </CardContent>
        </div>

        {/* Status Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 pt-6">
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold text-text-primary">
                Active Hunts
              </h3>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-text-muted">Running</span>
                  <span className="text-sm font-mono text-cyber-matrix-400">
                    {activeHunts.running}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-text-muted">Queued</span>
                  <span className="text-sm font-mono text-cyber-warning-400">
                    {activeHunts.queued}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-text-muted">Completed</span>
                  <span className="text-sm font-mono text-text-secondary">
                    {activeHunts.completed}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold text-text-primary">
                Detection Rate
              </h3>
            </CardHeader>
            <CardContent>
              <div className="text-center">
                <div className="text-3xl font-mono text-primary-400 mb-2">
                  {detectionRate.toFixed(1)}%
                </div>
                <div className="text-sm text-text-muted">Last 24 hours</div>
                <div className="mt-4 h-2 bg-background-tertiary rounded-full overflow-hidden">
                  <div
                    className="h-full bg-gradient-to-r from-primary-500 to-cyber-matrix-500 rounded-full transition-all duration-1000"
                    style={{ width: `${detectionRate}%` }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold text-text-primary">
                System Health
              </h3>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-text-muted">
                    Sources Online
                  </span>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-cyber-matrix-400 rounded-full animate-pulse" />
                    <span className="text-sm font-mono text-cyber-matrix-400">
                      2/2
                    </span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-text-muted">
                    Processing Load
                  </span>
                  <span className="text-sm font-mono text-text-primary">
                    {Math.round(systemHealth.processingLoad)}%
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-text-muted">Memory Usage</span>
                  <span className="text-sm font-mono text-text-primary">
                    {Math.round(systemHealth.memoryUsage)}%
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
