export interface ChartDataPoint {
  x: Date | number | string;
  y: number;
  [key: string]: any;
}
 
export interface ChartDataset {
  label: string;
  color: string;
  data: ChartDataPoint[];
  [key: string]: any;
}
 
export interface ChartTransformOptions {
  maxDataPoints?: number;
  sortByTime?: boolean;
  filterInvalid?: boolean;
  groupBy?: string;
}
 
/**
 * Predefined color palettes for charts
 */
export const CHART_COLOR_PALETTES = {
  default: [
    '#3B82F6',
    '#EF4444',
    '#10B981',
    '#F59E0B',
    '#8B5CF6',
    '#EC4899',
    '#06B6D4',
    '#84CC16',
    '#F97316',
    '#6366F1',
  ],
  cybersecurity: [
    '#dc2626', // red for critical
    '#f59e0b', // amber for high
    '#eab308', // yellow for medium
    '#22c55e', // green for low
    '#3b82f6', // blue for info
  ],
  status: {
    success: '#22c55e',
    warning: '#f59e0b',
    danger: '#dc2626',
    info: '#3b82f6',
    secondary: '#6b7280',
  },
} as const;
 
/**
 * Gets an array of colors for charts based on count needed
 */
export function getChartColors(
  count: number,
  palette: 'default' | 'cybersecurity' = 'default',
): string[] {
  const colors = CHART_COLOR_PALETTES[palette];
  const result: string[] = [];
 
  for (let i = 0; i < count; i++) {
    result.push(colors[i % colors.length]);
  }
 
  return result;
}
 
/**
 * Gets color for status/severity levels
 */
export function getStatusColor(status: string): string {
  const normalized = status.toLowerCase();
 
  switch (normalized) {
    case 'critical':
    case 'high':
    case 'danger':
    case 'error':
      return CHART_COLOR_PALETTES.status.danger;
 
    case 'warning':
    case 'medium':
    case 'warn':
      return CHART_COLOR_PALETTES.status.warning;
 
    case 'success':
    case 'low':
    case 'ok':
    case 'healthy':
      return CHART_COLOR_PALETTES.status.success;
 
    case 'info':
    case 'information':
      return CHART_COLOR_PALETTES.status.info;
 
    default:
      return CHART_COLOR_PALETTES.status.secondary;
  }
}
 
/**
 * Aggregates time series data points into fixed intervals (e.g., 5 minutes) using average.
 */
export function aggregateTimeSeries(
  data: Record<string, number>,
  intervalMs: number = 5 * 60 * 1000 // 5 minutes
): { x: Date; y: number }[] {
  if (!data) return [];
  const entries = Object.entries(data)
    .map(([ts, y]) => [Number(ts), y] as [number, number])
    .sort((a, b) => a[0] - b[0]);
  if (entries.length === 0) return [];
 
  const result: { x: Date; y: number }[] = [];
  let bucketStart = entries[0][0] - (entries[0][0] % intervalMs);
  let bucketEnd = bucketStart + intervalMs;
  let bucketVals: number[] = [];
 
  for (const [ts, y] of entries) {
    while (ts >= bucketEnd) {
      if (bucketVals.length > 0) {
        result.push({
          x: new Date(bucketStart),
          y: bucketVals.reduce((a, b) => a + b, 0) / bucketVals.length,
        });
      }
      bucketStart = bucketEnd;
      bucketEnd += intervalMs;
      bucketVals = [];
    }
    bucketVals.push(y);
  }
  if (bucketVals.length > 0) {
    result.push({
      x: new Date(bucketStart),
      y: bucketVals.reduce((a, b) => a + b, 0) / bucketVals.length,
    });
  }
  return result;
}
 
/**
 * Transforms raw data into chart-ready format
 */
export function transformForChart<T extends Record<string, any>>(
  data: T[],
  options: ChartTransformOptions = {},
): ChartDataset[] {
  const {
    maxDataPoints = 50,
    sortByTime = true,
    filterInvalid = true,
    groupBy,
  } = options;
 
  if (!data.length) return [];
 
  // Group data if groupBy is specified
  if (groupBy) {
    return transformGroupedData(data, groupBy, options);
  }
 
  // Simple transformation for single dataset
  const colors = getChartColors(1);
 
  const chartData = data
    .map((item, index) => ({
      x: item.timestamp ? new Date(item.timestamp) : new Date(),
      y: typeof item.value === 'number' ? item.value : 0,
      ...item,
    }))
    .filter((entry) => {
      if (!filterInvalid) return true;
      return !isNaN(entry.x.getTime()) && !isNaN(entry.y);
    });
 
  if (sortByTime) {
    chartData.sort((a, b) => a.x.getTime() - b.x.getTime());
  }
 
  // Limit data points
  const limitedData = chartData.slice(-maxDataPoints);
 
  return [
    {
      label: 'Data',
      color: colors[0],
      data: limitedData,
    },
  ];
}
 
/**
 * Transforms grouped data into multiple chart datasets
 */
function transformGroupedData<T extends Record<string, any>>(
  data: T[],
  groupBy: string,
  options: ChartTransformOptions,
): ChartDataset[] {
  const {
    maxDataPoints = 50,
    sortByTime = true,
    filterInvalid = true,
  } = options;
 
  // Group data by the specified field
  const groups = data.reduce(
    (acc, item) => {
      const groupKey = item[groupBy] || 'unknown';
      if (!acc[groupKey]) {
        acc[groupKey] = [];
      }
      acc[groupKey].push(item);
      return acc;
    },
    {} as Record<string, T[]>,
  );
 
  const colors = getChartColors(Object.keys(groups).length);
 
  return Object.entries(groups)
    .map(([groupKey, groupData], index) => {
      const chartData = groupData
        .map((item) => ({
          x: item.timestamp ? new Date(item.timestamp) : new Date(),
          y: typeof item.value === 'number' ? item.value : 0,
          ...item,
        }))
        .filter((entry) => {
          if (!filterInvalid) return true;
          return !isNaN(entry.x.getTime()) && !isNaN(entry.y);
        });
 
      if (sortByTime) {
        chartData.sort((a, b) => a.x.getTime() - b.x.getTime());
      }
 
      // Limit data points
      const limitedData = chartData.slice(-maxDataPoints);
 
      return {
        label: groupKey,
        color: colors[index],
        data: limitedData,
      };
    })
    .filter((dataset) => dataset.data.length > 0);
}
 
/**
 * Transforms deployment data specifically (matching the settings page pattern)
 * Handles both legacy format and modern format with timeseries/predictions
 * Aggregates into dynamically-sized intervals for readability
 */
export function transformDeploymentData(
  deployments: Array<{
    name: string;
    namespace: string;
    data: Record<string, number> | {
      timeseries: Record<string, number>;
      predictions: {
        "1min": Record<string, number>;
      };
      alerts: number[];
    };
    display_name?: string;
  }>,
  options?: {
    maxPoints?: number;
    minIntervalMs?: number;
  }
): Record<string, ChartDataset[]> {
  if (!deployments.length) return {};
 
  const colors = getChartColors(20);
  const result: Record<string, ChartDataset[]> = {};
  const maxPoints = options?.maxPoints ?? 25;

  const now = Date.now();
  const oneHourAgo = now - (60 * 60 * 1000);

  deployments.forEach((deployment, depIdx) => {
    const deploymentKey = `${deployment.name}-${deployment.namespace}`;
    const chartData: ChartDataset[] = [];
    
    const isModernFormat = deployment.data &&
      typeof deployment.data === 'object' &&
      'timeseries' in deployment.data &&
      'predictions' in deployment.data;

    if (isModernFormat) {
      const modernData = deployment.data as {
        timeseries: Record<string, number>;
        predictions: {
          "1min": Record<string, number>;
        };
        alerts: number[];
      };

      const timeseries = modernData.timeseries || {};
      const predictions = modernData.predictions?.["1min"] || {};

      // Check if we have actual distinct prediction data
      const hasPredictions = Object.keys(predictions).length > 0;
      const hasTimeseries = Object.keys(timeseries).length > 0;

      // Process timeseries data
      if (hasTimeseries) {
        const timeseriesEntries = Object.entries(timeseries)
          .map(([timestamp, value]) => {
            const ts = parseInt(timestamp);
            const normalizedTs = ts < 10000000000 ? ts * 1000 : ts;
            return {
              timestamp: normalizedTs,
              x: new Date(normalizedTs),
              y: value,
            };
          })
          .filter(entry => {
            const isInLastHour = entry.timestamp >= oneHourAgo && entry.timestamp <= now;
            const isValidData = !isNaN(entry.y) && !isNaN(entry.x.getTime());
            return isInLastHour && isValidData;
          })
          .sort((a, b) => a.x.getTime() - b.x.getTime())
          .slice(-maxPoints)
          .map(entry => ({ x: entry.x, y: entry.y }));

        if (timeseriesEntries.length > 0) {
          chartData.push({
            label: `${deployment.display_name || deployment.name} (Actual)`,
            color: colors[depIdx % colors.length],
            data: timeseriesEntries,
          });
        }
      }

      // Only process predictions if we have distinct prediction data
      if (hasPredictions) {
        const predictionsEntries = Object.entries(predictions)
          .map(([timestamp, value]) => {
            const ts = parseInt(timestamp);
            const normalizedTs = ts < 10000000000 ? ts * 1000 : ts;
            return {
              timestamp: normalizedTs,
              x: new Date(normalizedTs),
              y: value,
            };
          })
          .filter(entry => {
            const isInLastHour = entry.timestamp >= oneHourAgo && entry.timestamp <= now;
            const isValidData = !isNaN(entry.y) && !isNaN(entry.x.getTime());
            return isInLastHour && isValidData;
          })
          .sort((a, b) => a.x.getTime() - b.x.getTime())
          .slice(-maxPoints)
          .map(entry => ({ x: entry.x, y: entry.y }));

        if (predictionsEntries.length > 0) {
          chartData.push({
            label: `${deployment.display_name || deployment.name} (Predicted)`,
            color: '#ff6b6b',
            data: predictionsEntries,
          });
        }
      }
    } else {
      // Handle legacy format - only timeseries data available
      const legacyData = deployment.data as Record<string, number>;
      
      if (Object.keys(legacyData).length > 0) {
        const legacyEntries = Object.entries(legacyData)
          .map(([timestamp, value]) => {
            const ts = parseInt(timestamp);
            const normalizedTs = ts < 10000000000 ? ts * 1000 : ts;
            return {
              timestamp: normalizedTs,
              x: new Date(normalizedTs),
              y: value,
            };
          })
          .filter(entry => {
            const isInLastHour = entry.timestamp >= oneHourAgo && entry.timestamp <= now;
            const isValidData = !isNaN(entry.y) && !isNaN(entry.x.getTime());
            return isInLastHour && isValidData;
          })
          .sort((a, b) => a.x.getTime() - b.x.getTime())
          .slice(-maxPoints)
          .map(entry => ({ x: entry.x, y: entry.y }));

        if (legacyEntries.length > 0) {
          chartData.push({
            label: `${deployment.display_name || deployment.name}`,
            color: colors[depIdx % colors.length],
            data: legacyEntries,
          });
        }
      }
    }
    
    if (chartData.length > 0) {
      result[deploymentKey] = chartData;
    }
  });

  return result;
}