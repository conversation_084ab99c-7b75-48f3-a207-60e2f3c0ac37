#!/bin/bash

# Copy branding assets to public directory
BRANDING_ASSETS_PATH="../../../packages/frontend/branding/src/assets/logos"
BRANDING_IMAGES_PATH="../../../packages/frontend/branding/src/assets/images"
PUBLIC_LOGOS_PATH="public/logos"
PUBLIC_IMAGES_PATH="public/images"

echo "📦 Copying branding assets..."

# Create public directories if they don't exist
mkdir -p "$PUBLIC_LOGOS_PATH"
mkdir -p "$PUBLIC_IMAGES_PATH"

# Copy the specific logos
if [ -f "$BRANDING_ASSETS_PATH/full-logo-white-blue.png" ]; then
    cp "$BRANDING_ASSETS_PATH/full-logo-white-blue.png" "$PUBLIC_LOGOS_PATH/"
    echo "✅ Copied full-logo-white-blue.png"
else
    echo "⚠️  full-logo-white-blue.png not found"
    exit 1
fi

if [ -f "$BRANDING_ASSETS_PATH/full-logo.png" ]; then
    cp "$BRANDING_ASSETS_PATH/full-logo.png" "$PUBLIC_LOGOS_PATH/"
    echo "✅ Copied full-logo.png"
else
    echo "⚠️  full-logo.png not found"
    exit 1
fi

if [ -f "$BRANDING_ASSETS_PATH/t-logo.png" ]; then
    cp "$BRANDING_ASSETS_PATH/t-logo.png" "$PUBLIC_LOGOS_PATH/"
    echo "✅ Copied t-logo.png"
else
    echo "⚠️  t-logo.png not found"
    exit 1
fi

# Copy component images if they exist
if [ -d "$BRANDING_IMAGES_PATH" ]; then
    echo "📦 Copying component images..."

    # Copy Windows image
    if [ -f "$BRANDING_IMAGES_PATH/Windows.png" ]; then
        cp "$BRANDING_IMAGES_PATH/Windows.png" "$PUBLIC_IMAGES_PATH/"
        echo "✅ Copied Windows.png"
    fi

    # Copy Linux image
    if [ -f "$BRANDING_IMAGES_PATH/Linux.png" ]; then
        cp "$BRANDING_IMAGES_PATH/Linux.png" "$PUBLIC_IMAGES_PATH/"
        echo "✅ Copied Linux.png"
    fi

    # Copy MPAC6650 image
    if [ -f "$BRANDING_IMAGES_PATH/MPAC6650.png" ]; then
        cp "$BRANDING_IMAGES_PATH/MPAC6650.png" "$PUBLIC_IMAGES_PATH/"
        echo "✅ Copied MPAC6650.png"
    fi

    # Copy any additional images from the branding images folder
    for img in "$BRANDING_IMAGES_PATH"/*.{png,jpg,jpeg,gif,svg}; do
        if [ -f "$img" ]; then
            filename=$(basename "$img")
            if [ ! -f "$PUBLIC_IMAGES_PATH/$filename" ]; then
                cp "$img" "$PUBLIC_IMAGES_PATH/"
                echo "✅ Copied additional image: $filename"
            fi
        fi
    done
else
    echo "ℹ️  No component images directory found at $BRANDING_IMAGES_PATH"
fi

echo "🎉 Branding assets copied successfully!"
