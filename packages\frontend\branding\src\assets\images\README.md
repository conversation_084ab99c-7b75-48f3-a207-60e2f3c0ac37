# Component Images

This directory contains component images that are automatically copied to the frontend's public folder during the build process.

## Current Images

- `Windows.png` - Windows component image
- `Linux.png` - Linux component image  
- `MPAC6650.png` - MPAC6650 component image

## How it Works

1. Images are stored here in the branding package
2. During build, the `copy-branding-assets.sh` script automatically copies these images to `apps/frontend/intsoc/public/images/`
3. The images are then available in the built application at `/images/filename.png`

## Adding New Images

To add new component images:

1. Place the image files in this directory
2. The build script will automatically copy them to the public folder
3. Reference them in your React components using `/images/filename.png`

## Usage in Code

You can import the image paths from the branding package:

```typescript
import { COMPONENT_IMAGES } from '@telesoft/branding';

// Use in JSX
<img src={COMPONENT_IMAGES.windows} alt="Windows" />
<img src={COMPONENT_IMAGES.linux} alt="Linux" />
<img src={COMPONENT_IMAGES.mpac6650} alt="MPAC6650" />
```

Or reference them directly:

```jsx
<img src="/images/Windows.png" alt="Windows" />
<img src="/images/Linux.png" alt="Linux" />
<img src="/images/MPAC6650.png" alt="MPAC6650" />
```
