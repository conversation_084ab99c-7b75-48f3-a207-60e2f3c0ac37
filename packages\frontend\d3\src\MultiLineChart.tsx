import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';

export interface MultiLineChartDataPoint {
  x: number | Date;
  y: number;
  label?: string; // Optional label for custom x-axis display
}

export interface MultiLineChartDataset {
  label: string;
  data: MultiLineChartDataPoint[];
  color: string;
}

export interface MultiLineChartProps {
  datasets: MultiLineChartDataset[];
  width?: number;
  height?: number;
  margin?: { top: number; right: number; bottom: number; left: number };
  strokeWidth?: number;
  className?: string;
  xLabel?: string;
  yLabel?: string;
  showLegend?: boolean;
  legendLocation?: 'top' | 'right' | 'bottom' | 'left';
  legendAlign?: 'horizontal' | 'vertical';
  yMin?: number;
  yMax?: number;
}

const MultiLineChart: React.FC<MultiLineChartProps> = ({
  datasets,
  width = 400,
  height = 300,
  margin = { top: 20, right: 20, bottom: 40, left: 40 },
  strokeWidth = 2,
  className = '',
  xLabel,
  yLabel,
  showLegend = true,
  legendLocation = 'right',
  legendAlign = 'vertical',
  yMin,
  yMax,
}) => {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!svgRef.current || !datasets.length) {
      return;
    }

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove(); // Clear previous render

    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    const g = svg
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Combine all data points to find the global domain
    const allData = datasets.flatMap((dataset) => dataset.data);
    if (allData.length === 0) {
      return;
    }

    // Determine if we're dealing with date data
    const isDateData = datasets.some(dataset =>
      dataset.data.some(point => point.x instanceof Date)
    );

    const xExtent = isDateData
      ? (d3.extent(allData, (d: MultiLineChartDataPoint) => d.x as Date) as [
        Date,
        Date,
      ])
      : (d3.extent(allData, (d: MultiLineChartDataPoint) => d.x as number) as [
        number,
        number,
      ]);

    // Safety check for invalid extents
    if (!xExtent[0] || !xExtent[1]) {
      return;
    }

    const xScale = isDateData
      ? d3
        .scaleTime()
        .domain(xExtent as [Date, Date])
        .range([0, innerWidth])
      : d3
        .scaleLinear()
        .domain(xExtent as [number, number])
        .range([0, innerWidth]);

    const yExtent = d3.extent(allData, (d: MultiLineChartDataPoint) => d.y) as [
      number,
      number,
    ];


    let domainYMin: number;
    let domainYMax: number;

    if (yMin !== undefined) {
      domainYMin = yMin;
    } else {
      const dataYMin = Math.min(yExtent[0] || 0, 0);
      const dataYMax = Math.max(yExtent[1] || 1, dataYMin + 1);
      const yPadding = (dataYMax - dataYMin) * 0.1;
      domainYMin = dataYMin - yPadding;
    }

    if (yMax !== undefined) {
      domainYMax = yMax;
    } else {
      const dataYMax = Math.max(yExtent[1] || 1, (domainYMin || 0) + 1);
      const yPadding = yMin !== undefined ?
        (dataYMax - domainYMin) * 0.1 :
        (dataYMax - (yExtent[0] || 0)) * 0.1;
      domainYMax = dataYMax + yPadding;
    }

    const yScale = d3
      .scaleLinear()
      .domain([domainYMin, domainYMax])
      .range([innerHeight, 0]);

    // Create line generator with defined behavior
    const line = d3
      .line<MultiLineChartDataPoint>()
      .defined((d) => d.y != null && !isNaN(d.y))
      .x((d: MultiLineChartDataPoint) => {
        const xValue = isDateData
          ? (xScale as d3.ScaleTime<number, number>)(d.x as Date)
          : (xScale as d3.ScaleLinear<number, number>)(d.x as number);
        return xValue;
      })
      .y((d: MultiLineChartDataPoint) => {
        const yValue = yScale(d.y);
        return yValue;
      })
      .curve(d3.curveMonotoneX);

    // Add axes first so lines appear on top
    const xAxis = isDateData
      ? d3
        .axisBottom(xScale as d3.ScaleTime<number, number>)
        .ticks(6)
        .tickFormat((d) => {
          const date = d as Date;
          // For last hour data, show HH:MM format
          return d3.timeFormat('%H:%M')(date);
        })
      : d3.axisBottom(xScale as d3.ScaleLinear<number, number>)
        .tickFormat(d3.format('.0f'));

    g.append('g')
      .attr('transform', `translate(0,${innerHeight})`)
      .call(xAxis)
      .selectAll('text')
      .style('font-size', '11px')
      .attr('fill', 'currentColor')
      .style('color', 'var(--text-secondary)')
      .attr('transform', 'rotate(-45)')
      .style('text-anchor', 'end');

    // Style x-axis lines
    g.selectAll('g.tick line')
      .attr('stroke', 'currentColor')
      .style('color', 'var(--border-primary)')
      .attr('opacity', 0.3);

    g.select('.domain')
      .attr('stroke', 'currentColor')
      .style('color', 'var(--border-primary)');

    g.append('g')
      .call(d3.axisLeft(yScale).ticks(5))
      .selectAll('text')
      .style('font-size', '12px')
      .attr('fill', 'currentColor')
      .style('color', 'var(--text-secondary)');

    // Style y-axis lines
    g.selectAll('g:last-child .tick line')
      .attr('stroke', 'currentColor')
      .style('color', 'var(--border-primary)')
      .attr('opacity', 0.3);

    g.select('g:last-child .domain')
      .attr('stroke', 'currentColor')
      .style('color', 'var(--border-primary)');

    // Add lines for each dataset
    datasets.forEach((dataset, datasetIndex) => {
      if (dataset.data.length === 0) return;

      // Sort data by x value to ensure proper line drawing
      const sortedData = [...dataset.data].sort((a, b) => {
        if (isDateData) {
          return (a.x as Date).getTime() - (b.x as Date).getTime();
        }
        return (a.x as number) - (b.x as number);
      });

      // Test if line generator produces valid path
      const pathData = line(sortedData);

      if (!pathData) {
        console.error(`Failed to generate path for ${dataset.label}`);
        return;
      }

      // Add line path
      g.append('path')
        .datum(sortedData)
        .attr('fill', 'none')
        .attr('stroke', dataset.color)
        .attr('stroke-width', strokeWidth)
        .attr('stroke-linejoin', 'round')
        .attr('stroke-linecap', 'round')
        .attr('d', line);

      // Add points
      g.selectAll(`.point-${datasetIndex}`)
        .data(sortedData)
        .enter()
        .append('circle')
        .attr('class', `point-${datasetIndex}`)
        .attr('cx', (d: MultiLineChartDataPoint) => {
          const x = isDateData
            ? (xScale as d3.ScaleTime<number, number>)(d.x as Date)
            : (xScale as d3.ScaleLinear<number, number>)(d.x as number);
          return x;
        })
        .attr('cy', (d: MultiLineChartDataPoint) => yScale(d.y))
        .attr('r', 3)
        .attr('fill', dataset.color)
        .attr('stroke', '#fff')
        .attr('stroke-width', 0.5);
    });

    // Add axis labels
    if (xLabel) {
      g.append('text')
        .attr('x', innerWidth / 2)
        .attr('y', innerHeight + 35)
        .attr('text-anchor', 'middle')
        .style('font-size', '12px')
        .attr('fill', 'currentColor')
        .style('color', 'var(--text-secondary)')
        .text(xLabel);
    }

    if (yLabel) {
      g.append('text')
        .attr('transform', 'rotate(-90)')
        .attr('x', -innerHeight / 2)
        .attr('y', -25)
        .attr('text-anchor', 'middle')
        .style('font-size', '12px')
        .attr('fill', 'currentColor')
        .style('color', 'var(--text-secondary)')
        .text(yLabel);
    }

    // Add legend if requested
    if (showLegend && datasets.length > 1) {
      const legend = g.append('g').attr('class', 'legend');

      // Calculate legend positioning
      let legendX = 0;
      let legendY = 0;

      if (legendLocation === 'top') {
        if (legendAlign === 'horizontal') {
          // Horizontal legend at top center
          legendY = -15;

          // Calculate proper spacing for horizontal legend items
          let currentX = 0;
          const legendItems: {
            dataset: MultiLineChartDataset;
            width: number;
          }[] = [];

          // First pass: measure text widths
          datasets.forEach((dataset) => {
            // Create temporary text element to measure width
            const tempText = g
              .append('text')
              .style('font-size', '12px')
              .text(dataset.label)
              .style('visibility', 'hidden');

            const textNode = tempText.node();
            const textWidth = textNode ? textNode.getBBox().width : 0;
            const itemWidth = 12 + textWidth + 16; // dot + text + padding (reduced from 20 to 16)

            legendItems.push({ dataset, width: itemWidth });
            tempText.remove();
          });

          // Calculate total width and starting position
          const totalWidth = legendItems.reduce(
            (total, item) => total + item.width,
            0,
          );
          const startX = Math.max(0, (innerWidth - totalWidth) / 2);

          // Second pass: position legend items
          legendItems.forEach((item) => {
            const legendRow = legend
              .append('g')
              .attr('transform', `translate(${startX + currentX}, ${legendY})`);

            legendRow
              .append('circle')
              .attr('cx', 6)
              .attr('cy', 1)
              .attr('r', 4)
              .attr('fill', item.dataset.color);

            legendRow
              .append('text')
              .attr('x', 14)
              .attr('y', 4)
              .style('font-size', '12px')
              .attr('fill', 'currentColor')
              .style('color', 'var(--text-primary)')
              .text(item.dataset.label);

            currentX += item.width;
          });
        } else {
          // Vertical legend at top right
          legendX = innerWidth - 120;
          legendY = -15;
          legend.attr('transform', `translate(${legendX}, ${legendY})`);

          datasets.forEach((dataset, i) => {
            const legendRow = legend
              .append('g')
              .attr('transform', `translate(0, ${i * 20})`);

            legendRow
              .append('circle')
              .attr('cx', 6)
              .attr('cy', 1)
              .attr('r', 4)
              .attr('fill', dataset.color);

            legendRow
              .append('text')
              .attr('x', 14)
              .attr('y', 4)
              .style('font-size', '12px')
              .attr('fill', 'currentColor')
              .style('color', 'var(--text-primary)')
              .text(dataset.label);
          });
        }
      } else {
        // Default right side positioning (existing behavior)
        legendX = innerWidth - 120;
        legendY = 20;
        legend.attr('transform', `translate(${legendX}, ${legendY})`);

        datasets.forEach((dataset, i) => {
          const legendRow = legend
            .append('g')
            .attr('transform', `translate(0, ${i * 20})`);

          legendRow
            .append('circle')
            .attr('cx', 6)
            .attr('cy', 1)
            .attr('r', 4)
            .attr('fill', dataset.color);

          legendRow
            .append('text')
            .attr('x', 14)
            .attr('y', 4)
            .style('font-size', '12px')
            .attr('fill', 'currentColor')
            .style('color', 'var(--text-primary)')
            .text(dataset.label);
        });
      }
    }
  }, [
    datasets,
    width,
    height,
    margin,
    strokeWidth,
    xLabel,
    yLabel,
    showLegend,
    legendLocation,
    legendAlign,
    yMin,
    yMax,
  ]);

  return (
    <div className={className}>
      <svg
        ref={svgRef}
        width={width}
        height={height}
        style={{ display: 'block', background: 'rgba(0,0,0,0.02)' }}
      />
    </div>
  );
};

export { MultiLineChart };
export default MultiLineChart;






